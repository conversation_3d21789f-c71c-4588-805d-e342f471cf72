declare namespace cce {
    export interface ISceneEvents {
        'scene:ready': () => void;
        'scene:unload': () => void;
    }
}

declare namespace Editor {
    export interface IMainEvents {
        'asset-db:asset-change': (uuid: string, info: any) => void;
        'asset-db:asset-delete': (uuid: string, info: any) => void;
    }

    export namespace Project {
        export const path: string;
    }

    export namespace Panel {
        export function open(panelName: string): void;
    }

    export namespace Message {
        export function request(extensionName: string, methodName: string, ...args: any[]): Promise<any>;
    }
}
