/**
 * UUID 使用情況檢查面板
 */
interface UuidCheckResult {
    uuid: string;
    assetPath: string;
    references: string[];
    isOrphan: boolean;
}
export declare const template = "\n<div class=\"container\">\n    <div class=\"header\">\n        <h2>UUID \u4F7F\u7528\u60C5\u6CC1\u6AA2\u67E5\u5668</h2>\n        <ui-button class=\"scan-btn\" @click=\"onScanClick\">\u6383\u63CF\u5C08\u6848</ui-button>\n    </div>\n    \n    <div class=\"summary\" v-if=\"summary\">\n        <div class=\"summary-item\">\n            <span class=\"label\">\u7E3D\u8CC7\u6E90\u6578:</span>\n            <span class=\"value\">{{ summary.totalAssets }}</span>\n        </div>\n        <div class=\"summary-item\">\n            <span class=\"label\">\u5DF2\u5F15\u7528\u8CC7\u6E90:</span>\n            <span class=\"value\">{{ summary.referencedAssets }}</span>\n        </div>\n        <div class=\"summary-item orphan\">\n            <span class=\"label\">\u5B64\u7ACB\u8CC7\u6E90:</span>\n            <span class=\"value\">{{ summary.orphanAssets }}</span>\n        </div>\n    </div>\n    \n    <div class=\"content\">\n        <div v-if=\"loading\" class=\"loading\">\n            \u6B63\u5728\u6383\u63CF\u5C08\u6848...\n        </div>\n        \n        <div v-else-if=\"error\" class=\"error\">\n            \u932F\u8AA4: {{ error }}\n        </div>\n        \n        <div v-else-if=\"results.length > 0\" class=\"results\">\n            <div class=\"filter-tabs\">\n                <ui-button \n                    :class=\"{ active: currentFilter === 'all' }\" \n                    @click=\"setFilter('all')\">\n                    \u5168\u90E8 ({{ results.length }})\n                </ui-button>\n                <ui-button \n                    :class=\"{ active: currentFilter === 'orphan' }\" \n                    @click=\"setFilter('orphan')\">\n                    \u5B64\u7ACB\u8CC7\u6E90 ({{ orphanResults.length }})\n                </ui-button>\n                <ui-button \n                    :class=\"{ active: currentFilter === 'referenced' }\" \n                    @click=\"setFilter('referenced')\">\n                    \u5DF2\u5F15\u7528 ({{ referencedResults.length }})\n                </ui-button>\n            </div>\n            \n            <div class=\"result-list\">\n                <div \n                    v-for=\"result in filteredResults\" \n                    :key=\"result.uuid\" \n                    :class=\"{ 'result-item': true, 'orphan': result.isOrphan }\">\n                    <div class=\"asset-info\">\n                        <div class=\"asset-path\">{{ result.assetPath }}</div>\n                        <div class=\"uuid\">UUID: {{ result.uuid }}</div>\n                    </div>\n                    <div class=\"references\" v-if=\"result.references.length > 0\">\n                        <div class=\"references-title\">\u5F15\u7528\u4F4D\u7F6E:</div>\n                        <div \n                            v-for=\"ref in result.references\" \n                            :key=\"ref\" \n                            class=\"reference-item\">\n                            {{ ref }}\n                        </div>\n                    </div>\n                    <div v-else class=\"no-references\">\n                        \u7121\u5F15\u7528\n                    </div>\n                </div>\n            </div>\n        </div>\n        \n        <div v-else class=\"empty\">\n            \u9EDE\u64CA\u300C\u6383\u63CF\u5C08\u6848\u300D\u958B\u59CB\u6AA2\u67E5 UUID \u4F7F\u7528\u60C5\u6CC1\n        </div>\n    </div>\n</div>\n";
export declare const style = "\n.container {\n    padding: 10px;\n    height: 100%;\n    display: flex;\n    flex-direction: column;\n}\n\n.header {\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n    margin-bottom: 15px;\n    padding-bottom: 10px;\n    border-bottom: 1px solid var(--color-normal-border);\n}\n\n.header h2 {\n    margin: 0;\n    color: var(--color-default-contrast-emphasis);\n}\n\n.scan-btn {\n    min-width: 100px;\n}\n\n.summary {\n    display: flex;\n    gap: 20px;\n    margin-bottom: 15px;\n    padding: 10px;\n    background: var(--color-normal-fill-emphasis);\n    border-radius: 4px;\n}\n\n.summary-item {\n    display: flex;\n    flex-direction: column;\n    align-items: center;\n}\n\n.summary-item .label {\n    font-size: 12px;\n    color: var(--color-default-contrast-weaken);\n    margin-bottom: 4px;\n}\n\n.summary-item .value {\n    font-size: 18px;\n    font-weight: bold;\n    color: var(--color-default-contrast-emphasis);\n}\n\n.summary-item.orphan .value {\n    color: var(--color-warn-contrast-emphasis);\n}\n\n.content {\n    flex: 1;\n    overflow: hidden;\n    display: flex;\n    flex-direction: column;\n}\n\n.loading, .error, .empty {\n    display: flex;\n    justify-content: center;\n    align-items: center;\n    height: 200px;\n    color: var(--color-default-contrast-weaken);\n}\n\n.error {\n    color: var(--color-warn-contrast-emphasis);\n}\n\n.filter-tabs {\n    display: flex;\n    gap: 5px;\n    margin-bottom: 10px;\n}\n\n.filter-tabs ui-button.active {\n    background: var(--color-info-fill);\n    color: var(--color-info-contrast);\n}\n\n.result-list {\n    flex: 1;\n    overflow-y: auto;\n}\n\n.result-item {\n    padding: 10px;\n    margin-bottom: 8px;\n    border: 1px solid var(--color-normal-border);\n    border-radius: 4px;\n    background: var(--color-normal-fill);\n}\n\n.result-item.orphan {\n    border-color: var(--color-warn-border);\n    background: var(--color-warn-fill-weaken);\n}\n\n.asset-info {\n    margin-bottom: 8px;\n}\n\n.asset-path {\n    font-weight: bold;\n    color: var(--color-default-contrast-emphasis);\n    margin-bottom: 4px;\n}\n\n.uuid {\n    font-size: 12px;\n    color: var(--color-default-contrast-weaken);\n    font-family: monospace;\n}\n\n.references-title {\n    font-size: 12px;\n    color: var(--color-default-contrast-weaken);\n    margin-bottom: 4px;\n}\n\n.reference-item {\n    font-size: 12px;\n    color: var(--color-info-contrast-emphasis);\n    margin-left: 10px;\n    margin-bottom: 2px;\n}\n\n.no-references {\n    font-size: 12px;\n    color: var(--color-warn-contrast-emphasis);\n    font-style: italic;\n}\n";
export declare const $: {
    container: string;
};
export declare const methods: {
    onScanClick(): Promise<void>;
    setFilter(filter: string): void;
};
export declare const data: {
    loading: boolean;
    error: any;
    results: UuidCheckResult[];
    summary: any;
    currentFilter: string;
};
export declare const computed: {
    orphanResults(): any;
    referencedResults(): any;
    filteredResults(): any;
};
export declare const ready: () => void;
export declare const close: () => void;
export {};
