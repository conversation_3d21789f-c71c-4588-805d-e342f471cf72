/**
 * 簡單的空白面板測試
 */
export declare const template = "\n<div class=\"container\">\n    <h1>UUID \u6AA2\u67E5\u5DE5\u5177</h1>\n    <p>\u9762\u677F\u5DF2\u6210\u529F\u8F09\u5165\uFF01</p>\n    <ui-button @click=\"onTestClick\">\u6E2C\u8A66\u6309\u9215</ui-button>\n    <div v-if=\"message\">{{ message }}</div>\n</div>\n";
export declare const style = "\n.container {\n    padding: 20px;\n    text-align: center;\n}\n\n.container h1 {\n    color: var(--color-default-contrast-emphasis);\n    margin-bottom: 20px;\n}\n\n.container p {\n    color: var(--color-default-contrast-weaken);\n    margin-bottom: 20px;\n}\n\n.container ui-button {\n    margin: 10px;\n}\n";
export declare const $: {
    container: string;
};
export declare const methods: {
    onTestClick(): void;
};
export declare const data: {
    message: string;
};
export declare const ready: () => void;
export declare const close: () => void;
