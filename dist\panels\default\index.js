"use strict";
/**
 * 簡單的空白面板測試
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.close = exports.ready = exports.data = exports.methods = exports.$ = exports.style = exports.template = void 0;
exports.template = `
<div class="container">
    <h1>UUID 檢查工具</h1>
    <p>面板已成功載入！</p>
    <ui-button @click="onTestClick">測試按鈕</ui-button>
    <div v-if="message">{{ message }}</div>
</div>
`;
exports.style = `
.container {
    padding: 20px;
    text-align: center;
}

.container h1 {
    color: var(--color-default-contrast-emphasis);
    margin-bottom: 20px;
}

.container p {
    color: var(--color-default-contrast-weaken);
    margin-bottom: 20px;
}

.container ui-button {
    margin: 10px;
}
`;
exports.$ = {
    container: '.container'
};
exports.methods = {
    onTestClick() {
        this.message = '按鈕點擊成功！時間: ' + new Date().toLocaleTimeString();
        console.log('測試按鈕被點擊了');
    }
};
exports.data = {
    message: ''
};
const ready = function () {
    console.log('UUID 檢查面板已準備就緒');
};
exports.ready = ready;
const close = function () {
    console.log('UUID 檢查面板已關閉');
};
exports.close = close;
