"use strict";
/**
 * UUID 使用情況檢查面板
 */
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.close = exports.ready = exports.computed = exports.data = exports.methods = exports.$ = exports.style = exports.template = void 0;
exports.template = `
<div class="container">
    <div class="header">
        <h2>UUID 使用情況檢查器</h2>
        <ui-button class="scan-btn" @click="onScanClick">掃描專案</ui-button>
    </div>
    
    <div class="summary" v-if="summary">
        <div class="summary-item">
            <span class="label">總資源數:</span>
            <span class="value">{{ summary.totalAssets }}</span>
        </div>
        <div class="summary-item">
            <span class="label">已引用資源:</span>
            <span class="value">{{ summary.referencedAssets }}</span>
        </div>
        <div class="summary-item orphan">
            <span class="label">孤立資源:</span>
            <span class="value">{{ summary.orphanAssets }}</span>
        </div>
    </div>
    
    <div class="content">
        <div v-if="loading" class="loading">
            正在掃描專案...
        </div>
        
        <div v-else-if="error" class="error">
            錯誤: {{ error }}
        </div>
        
        <div v-else-if="results.length > 0" class="results">
            <div class="filter-tabs">
                <ui-button 
                    :class="{ active: currentFilter === 'all' }" 
                    @click="setFilter('all')">
                    全部 ({{ results.length }})
                </ui-button>
                <ui-button 
                    :class="{ active: currentFilter === 'orphan' }" 
                    @click="setFilter('orphan')">
                    孤立資源 ({{ orphanResults.length }})
                </ui-button>
                <ui-button 
                    :class="{ active: currentFilter === 'referenced' }" 
                    @click="setFilter('referenced')">
                    已引用 ({{ referencedResults.length }})
                </ui-button>
            </div>
            
            <div class="result-list">
                <div 
                    v-for="result in filteredResults" 
                    :key="result.uuid" 
                    :class="{ 'result-item': true, 'orphan': result.isOrphan }">
                    <div class="asset-info">
                        <div class="asset-path">{{ result.assetPath }}</div>
                        <div class="uuid">UUID: {{ result.uuid }}</div>
                    </div>
                    <div class="references" v-if="result.references.length > 0">
                        <div class="references-title">引用位置:</div>
                        <div 
                            v-for="ref in result.references" 
                            :key="ref" 
                            class="reference-item">
                            {{ ref }}
                        </div>
                    </div>
                    <div v-else class="no-references">
                        無引用
                    </div>
                </div>
            </div>
        </div>
        
        <div v-else class="empty">
            點擊「掃描專案」開始檢查 UUID 使用情況
        </div>
    </div>
</div>
`;
exports.style = `
.container {
    padding: 10px;
    height: 100%;
    display: flex;
    flex-direction: column;
}

.header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid var(--color-normal-border);
}

.header h2 {
    margin: 0;
    color: var(--color-default-contrast-emphasis);
}

.scan-btn {
    min-width: 100px;
}

.summary {
    display: flex;
    gap: 20px;
    margin-bottom: 15px;
    padding: 10px;
    background: var(--color-normal-fill-emphasis);
    border-radius: 4px;
}

.summary-item {
    display: flex;
    flex-direction: column;
    align-items: center;
}

.summary-item .label {
    font-size: 12px;
    color: var(--color-default-contrast-weaken);
    margin-bottom: 4px;
}

.summary-item .value {
    font-size: 18px;
    font-weight: bold;
    color: var(--color-default-contrast-emphasis);
}

.summary-item.orphan .value {
    color: var(--color-warn-contrast-emphasis);
}

.content {
    flex: 1;
    overflow: hidden;
    display: flex;
    flex-direction: column;
}

.loading, .error, .empty {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 200px;
    color: var(--color-default-contrast-weaken);
}

.error {
    color: var(--color-warn-contrast-emphasis);
}

.filter-tabs {
    display: flex;
    gap: 5px;
    margin-bottom: 10px;
}

.filter-tabs ui-button.active {
    background: var(--color-info-fill);
    color: var(--color-info-contrast);
}

.result-list {
    flex: 1;
    overflow-y: auto;
}

.result-item {
    padding: 10px;
    margin-bottom: 8px;
    border: 1px solid var(--color-normal-border);
    border-radius: 4px;
    background: var(--color-normal-fill);
}

.result-item.orphan {
    border-color: var(--color-warn-border);
    background: var(--color-warn-fill-weaken);
}

.asset-info {
    margin-bottom: 8px;
}

.asset-path {
    font-weight: bold;
    color: var(--color-default-contrast-emphasis);
    margin-bottom: 4px;
}

.uuid {
    font-size: 12px;
    color: var(--color-default-contrast-weaken);
    font-family: monospace;
}

.references-title {
    font-size: 12px;
    color: var(--color-default-contrast-weaken);
    margin-bottom: 4px;
}

.reference-item {
    font-size: 12px;
    color: var(--color-info-contrast-emphasis);
    margin-left: 10px;
    margin-bottom: 2px;
}

.no-references {
    font-size: 12px;
    color: var(--color-warn-contrast-emphasis);
    font-style: italic;
}
`;
exports.$ = {
    container: '.container'
};
exports.methods = {
    onScanClick() {
        return __awaiter(this, void 0, void 0, function* () {
            this.loading = true;
            this.error = null;
            try {
                const result = yield Editor.Message.request('checkUuidUsage', 'getUuidUsageData');
                if (result.success && result.data) {
                    this.results = result.data;
                    this.summary = result.summary;
                }
                else {
                    this.error = result.error || '掃描失敗';
                }
            }
            catch (error) {
                this.error = `掃描過程中發生錯誤: ${error.message}`;
            }
            finally {
                this.loading = false;
            }
        });
    },
    setFilter(filter) {
        this.currentFilter = filter;
    }
};
exports.data = {
    loading: false,
    error: null,
    results: [],
    summary: null,
    currentFilter: 'all'
};
exports.computed = {
    orphanResults() {
        return this.results.filter((r) => r.isOrphan);
    },
    referencedResults() {
        return this.results.filter((r) => !r.isOrphan);
    },
    filteredResults() {
        switch (this.currentFilter) {
            case 'orphan':
                return this.orphanResults;
            case 'referenced':
                return this.referencedResults;
            default:
                return this.results;
        }
    }
};
const ready = function () {
    console.log('UUID 使用情況檢查面板已準備就緒');
};
exports.ready = ready;
const close = function () {
    console.log('UUID 使用情況檢查面板已關閉');
};
exports.close = close;
