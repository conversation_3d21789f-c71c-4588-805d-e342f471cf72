# checkUuidUsage 使用指南

## 安裝步驟

### 1. 環境準備
確保您的系統已安裝：
- Node.js >= 16.0.1
- Cocos Creator >= 3.6.2

### 2. 快速安裝
在專案根目錄執行：

**Windows:**
```bash
setup.bat
```

**macOS/Linux:**
```bash
chmod +x setup.sh
./setup.sh
```

**手動安裝:**
```bash
npm install
npm run build
```

### 3. 在 Cocos Creator 中啟用擴展

1. 打開 Cocos Creator 3.6.2
2. 選擇頂部選單 **擴展 -> 擴展管理器 -> 全局**
3. 找到 `checkUuidUsage` 擴展
4. 點擊 **啟用** 按鈕

## 使用方法

### 方法一：控制台檢查
1. 在 Cocos Creator 頂部選單選擇 **Developer -> Check UUID Usage**
2. 查看控制台輸出的檢查結果

### 方法二：面板界面
1. 在 Cocos Creator 頂部選單選擇 **Developer -> UUID Usage Panel**
2. 在打開的面板中點擊 **掃描專案** 按鈕
3. 查看詳細的 UUID 使用情況報告

## 功能說明

### 主要功能
- **UUID 掃描**: 自動掃描專案中所有資源的 UUID
- **引用分析**: 分析每個 UUID 在專案中的引用情況
- **孤立資源檢測**: 識別沒有被任何地方引用的資源
- **分類顯示**: 按全部、孤立資源、已引用資源分類顯示

### 掃描範圍
- 場景文件 (*.scene)
- 預製體文件 (*.prefab)
- 腳本文件 (*.ts, *.js)
- 資源元數據文件 (*.meta)

### 報告內容
- 總資源數量
- 已引用資源數量
- 孤立資源數量
- 每個資源的詳細引用位置

## 開發模式

### 監聽模式
```bash
npm run watch
```
啟動後，修改源代碼會自動重新編譯。

### 調試
1. 打開 Cocos Creator 控制台 (Ctrl+Shift+I 或 Cmd+Option+I)
2. 查看擴展的日誌輸出
3. 檢查錯誤信息

## 故障排除

### 常見問題

**Q: 擴展無法加載**
A: 
- 確保已執行 `npm run build` 編譯代碼
- 檢查 Cocos Creator 版本是否 >= 3.6.2
- 重新啟動 Cocos Creator

**Q: 面板無法打開**
A:
- 確保擴展已在擴展管理器中啟用
- 檢查控制台是否有錯誤信息
- 嘗試重新啟用擴展

**Q: 掃描結果不準確**
A:
- 確保專案路徑正確
- 檢查文件權限
- 確認資源文件格式正確

**Q: 編譯錯誤**
A:
- 檢查 Node.js 版本是否 >= 16.0.1
- 刪除 node_modules 文件夾後重新執行 `npm install`
- 檢查 TypeScript 版本是否 >= 4.3.4

### 重新安裝
如果遇到問題，可以嘗試重新安裝：

```bash
# 清理
rm -rf node_modules dist

# 重新安裝
npm install
npm run build
```

## 自定義配置

可以修改 `src/config.ts` 文件來自定義掃描行為：

```typescript
export const defaultConfig: ScanConfig = {
    scanExtensions: ['.scene', '.prefab', '.ts', '.js'],  // 掃描的文件類型
    excludeDirectories: ['node_modules', '.git'],         // 排除的目錄
    excludePatterns: ['*.tmp', '*.bak'],                  // 排除的文件模式
    verbose: false,                                       // 是否顯示詳細日誌
    maxFiles: 10000                                       // 最大掃描文件數
};
```

修改後需要重新編譯：
```bash
npm run build
```

## 技術支持

如果您遇到問題或有建議，請：
1. 檢查控制台錯誤信息
2. 確認環境配置正確
3. 查看本文檔的故障排除部分

## 版本信息
- 當前版本: 1.0.0
- 支持的 Cocos Creator 版本: >= 3.6.2
- Node.js 要求: >= 16.0.1
- TypeScript 版本: >= 4.3.4
