/**
 * @en Registration method for the main process of Extension
 * @zh 為擴展的主進程的註冊方法
 */
export declare const methods: {
    [key: string]: (...any: any) => any;
};
/**
 * @en Hooks triggered after extension loading is complete
 * @zh 擴展加載完成後觸發的鉤子
 */
export declare const load: () => void;
/**
 * @en Hooks triggered after extension uninstallation is complete
 * @zh 擴展卸載完成後觸發的鉤子
 */
export declare const unload: () => void;
