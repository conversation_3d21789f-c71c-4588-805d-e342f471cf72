@echo off
echo 正在設置 checkUuidUsage 擴展開發環境...

echo.
echo 1. 安裝 Node.js 依賴...
call npm install

echo.
echo 2. 編譯 TypeScript 代碼...
call npm run build

echo.
echo 3. 設置完成！
echo.
echo 使用說明:
echo - 在 Cocos Creator 中打開 擴展 ^> 擴展管理器 ^> 全局
echo - 找到 checkUuidUsage 擴展並啟用
echo - 在頂部選單選擇 Developer ^> Check UUID Usage 或 UUID Usage Panel
echo.
echo 開發模式:
echo - 執行 npm run watch 來啟動監聽模式
echo - 修改代碼後會自動重新編譯
echo.
pause
