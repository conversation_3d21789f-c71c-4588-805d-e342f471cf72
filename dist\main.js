"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.unload = exports.load = exports.methods = void 0;
const fs = __importStar(require("fs"));
const path = __importStar(require("path"));
const config_1 = require("./config");
/**
 * @en Registration method for the main process of Extension
 * @zh 為擴展的主進程的註冊方法
 */
exports.methods = {
    /**
     * 檢查 UUID 使用情況
     */
    checkUuidUsage() {
        return __awaiter(this, void 0, void 0, function* () {
            console.log('開始檢查 UUID 使用情況...');
            try {
                const projectPath = Editor.Project.path;
                if (!projectPath) {
                    console.error('無法獲取專案路徑');
                    return;
                }
                const assetsPath = path.join(projectPath, 'assets');
                const results = yield scanUuidUsage(assetsPath);
                // 輸出結果
                console.log(`=== UUID 使用情況報告 ===`);
                console.log(`總共掃描到 ${results.length} 個資源`);
                const orphanAssets = results.filter(r => r.isOrphan);
                if (orphanAssets.length > 0) {
                    console.log(`\n發現 ${orphanAssets.length} 個孤立資源:`);
                    orphanAssets.forEach(asset => {
                        console.log(`- ${asset.assetPath} (UUID: ${asset.uuid})`);
                    });
                }
                else {
                    console.log('\n沒有發現孤立資源');
                }
                console.log('\nUUID 檢查完成');
            }
            catch (error) {
                console.error('UUID 檢查過程中發生錯誤:', error);
            }
        });
    },
    /**
     * 打開面板
     */
    openPanel() {
        Editor.Panel.open('checkUuidUsage.default');
    },
    /**
     * 獲取 UUID 使用情況數據（供面板使用）
     */
    getUuidUsageData() {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                const projectPath = Editor.Project.path;
                if (!projectPath) {
                    return { error: '無法獲取專案路徑' };
                }
                const assetsPath = path.join(projectPath, 'assets');
                const results = yield scanUuidUsage(assetsPath);
                return {
                    success: true,
                    data: results,
                    summary: {
                        totalAssets: results.length,
                        orphanAssets: results.filter(r => r.isOrphan).length,
                        referencedAssets: results.filter(r => !r.isOrphan).length
                    }
                };
            }
            catch (error) {
                return { error: error.message };
            }
        });
    }
};
/**
 * 掃描 UUID 使用情況
 */
function scanUuidUsage(assetsPath) {
    return __awaiter(this, void 0, void 0, function* () {
        const config = (0, config_1.getConfig)();
        const results = [];
        const allFiles = getAllFiles(assetsPath, config);
        if (config.verbose) {
            console.log(`開始掃描 ${allFiles.length} 個文件...`);
        }
        // 收集所有 .meta 文件中的 UUID
        const uuidToAssetMap = new Map();
        const metaFiles = allFiles.filter(file => file.endsWith('.meta'));
        for (const metaFile of metaFiles) {
            try {
                const metaContent = fs.readFileSync(metaFile, 'utf8');
                const metaData = JSON.parse(metaContent);
                if (metaData.uuid) {
                    const assetPath = metaFile.replace('.meta', '');
                    uuidToAssetMap.set(metaData.uuid, assetPath);
                }
            }
            catch (error) {
                if (config.verbose) {
                    console.warn(`無法解析 meta 文件: ${metaFile}`, error);
                }
            }
        }
        if (config.verbose) {
            console.log(`找到 ${uuidToAssetMap.size} 個資源 UUID`);
        }
        // 掃描所有文件中的 UUID 引用
        const uuidReferences = new Map();
        const contentFiles = allFiles.filter(file => (0, config_1.shouldScanFile)(file, config) && !file.endsWith('.meta'));
        for (const file of contentFiles) {
            try {
                const content = fs.readFileSync(file, 'utf8');
                const uuids = extractUuidsFromContent(content);
                for (const uuid of uuids) {
                    if (!uuidReferences.has(uuid)) {
                        uuidReferences.set(uuid, []);
                    }
                    uuidReferences.get(uuid).push(file);
                }
            }
            catch (error) {
                if (config.verbose) {
                    console.warn(`無法讀取文件: ${file}`, error);
                }
            }
        }
        if (config.verbose) {
            console.log(`掃描了 ${contentFiles.length} 個內容文件`);
        }
        // 生成結果
        for (const [uuid, assetPath] of uuidToAssetMap) {
            const references = uuidReferences.get(uuid) || [];
            const isOrphan = references.length === 0;
            results.push({
                uuid,
                assetPath: path.relative(assetsPath, assetPath),
                references: references.map(ref => path.relative(assetsPath, ref)),
                isOrphan
            });
        }
        return results;
    });
}
/**
 * 獲取目錄下所有文件
 */
function getAllFiles(dirPath, config) {
    const files = [];
    let fileCount = 0;
    function walkDir(currentPath) {
        try {
            if (fileCount >= config.maxFiles) {
                console.warn(`已達到最大文件數量限制: ${config.maxFiles}`);
                return;
            }
            if (!(0, config_1.shouldScanDirectory)(currentPath, config)) {
                return;
            }
            const items = fs.readdirSync(currentPath);
            for (const item of items) {
                const fullPath = path.join(currentPath, item);
                const stat = fs.statSync(fullPath);
                if (stat.isDirectory()) {
                    walkDir(fullPath);
                }
                else {
                    files.push(fullPath);
                    fileCount++;
                    if (fileCount >= config.maxFiles) {
                        break;
                    }
                }
            }
        }
        catch (error) {
            if (config.verbose) {
                console.warn(`無法讀取目錄: ${currentPath}`, error);
            }
        }
    }
    walkDir(dirPath);
    return files;
}
/**
 * 從文件內容中提取 UUID
 */
function extractUuidsFromContent(content) {
    const uuids = [];
    // UUID 正則表達式 (36 字符的標準 UUID 格式)
    const uuidRegex = /[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}/gi;
    let match;
    while ((match = uuidRegex.exec(content)) !== null) {
        if (!uuids.includes(match[0])) {
            uuids.push(match[0]);
        }
    }
    return uuids;
}
/**
 * @en Hooks triggered after extension loading is complete
 * @zh 擴展加載完成後觸發的鉤子
 */
const load = function () {
    console.log('checkUuidUsage 擴展已加載');
};
exports.load = load;
/**
 * @en Hooks triggered after extension uninstallation is complete
 * @zh 擴展卸載完成後觸發的鉤子
 */
const unload = function () {
    console.log('checkUuidUsage 擴展已卸載');
};
exports.unload = unload;
