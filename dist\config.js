"use strict";
/**
 * UUID 檢查工具配置
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.shouldScanDirectory = exports.shouldScanFile = exports.getConfig = exports.defaultConfig = void 0;
/**
 * 默認配置
 */
exports.defaultConfig = {
    scanExtensions: [
        '.scene',
        '.prefab',
        '.ts',
        '.js',
        '.json',
        '.meta'
    ],
    excludeDirectories: [
        'node_modules',
        '.git',
        'temp',
        'library',
        'local',
        'build'
    ],
    excludePatterns: [
        '*.tmp',
        '*.temp',
        '*.bak',
        '*~'
    ],
    verbose: false,
    maxFiles: 10000
};
/**
 * 獲取配置
 */
function getConfig() {
    // 這裡可以從用戶設置或配置文件中讀取自定義配置
    // 目前返回默認配置
    return Object.assign({}, exports.defaultConfig);
}
exports.getConfig = getConfig;
/**
 * 檢查文件是否應該被掃描
 */
function shouldScanFile(filePath, config) {
    const fileName = filePath.toLowerCase();
    // 檢查擴展名
    const hasValidExtension = config.scanExtensions.some(ext => fileName.endsWith(ext.toLowerCase()));
    if (!hasValidExtension) {
        return false;
    }
    // 檢查排除模式
    const isExcluded = config.excludePatterns.some(pattern => {
        const regex = new RegExp(pattern.replace(/\*/g, '.*'));
        return regex.test(fileName);
    });
    return !isExcluded;
}
exports.shouldScanFile = shouldScanFile;
/**
 * 檢查目錄是否應該被掃描
 */
function shouldScanDirectory(dirPath, config) {
    const dirName = dirPath.toLowerCase();
    return !config.excludeDirectories.some(excludeDir => dirName.includes(excludeDir.toLowerCase()));
}
exports.shouldScanDirectory = shouldScanDirectory;
