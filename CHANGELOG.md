# 更新日誌

## [1.0.0] - 2024-08-04

### 新增功能
- ✨ 初始版本發布
- ✨ UUID 掃描功能：自動掃描專案中所有資源的 UUID
- ✨ 引用分析：分析每個 UUID 在專案中的引用情況
- ✨ 孤立資源檢測：識別沒有被任何地方引用的資源
- ✨ 控制台輸出：在控制台顯示簡潔的檢查結果
- ✨ 面板界面：提供友好的圖形化界面
- ✨ 分類顯示：按全部、孤立資源、已引用資源分類顯示
- ✨ 多語言支持：支持中文和英文界面
- ✨ 可配置掃描：支持自定義掃描範圍和行為

### 技術特性
- 🔧 TypeScript 開發：使用 TypeScript 確保代碼質量
- 🔧 模塊化設計：清晰的代碼結構和模塊劃分
- 🔧 錯誤處理：完善的錯誤處理和用戶提示
- 🔧 性能優化：支持大型專案的高效掃描
- 🔧 開發友好：提供監聽模式和調試支持

### 支持的文件類型
- 📁 場景文件 (*.scene)
- 📁 預製體文件 (*.prefab)
- 📁 腳本文件 (*.ts, *.js)
- 📁 資源元數據文件 (*.meta)

### 系統要求
- 🎯 Cocos Creator >= 3.6.2
- 🎯 Node.js >= 16.0.1
- 🎯 TypeScript >= 4.3.4

### 安裝方式
- 📦 自動安裝腳本 (setup.bat / setup.sh)
- 📦 手動安裝 (npm install + npm run build)
- 📦 開發模式 (npm run watch)

---

## 計劃中的功能

### [1.1.0] - 計劃中
- 🚀 資源依賴圖：視覺化顯示資源之間的依賴關係
- 🚀 批量清理：一鍵清理孤立資源
- 🚀 導出報告：將檢查結果導出為 CSV 或 JSON 格式
- 🚀 增量掃描：只掃描變更的文件以提升性能

### [1.2.0] - 計劃中
- 🚀 資源使用統計：統計每個資源的使用頻率
- 🚀 重複資源檢測：找出可能重複的資源
- 🚀 資源大小分析：分析資源佔用空間
- 🚀 自動化建議：提供專案優化建議

---

## 貢獻指南

歡迎提交 Issue 和 Pull Request！

### 開發環境設置
```bash
git clone <repository>
cd checkUuidUsage
npm install
npm run watch
```

### 提交規範
- feat: 新功能
- fix: 修復 bug
- docs: 文檔更新
- style: 代碼格式調整
- refactor: 代碼重構
- test: 測試相關
- chore: 構建過程或輔助工具的變動
