/**
 * Cocos Creator 編輯器全局類型定義
 */

declare global {
    namespace Editor {
        namespace Project {
            const path: string;
        }
        
        namespace Panel {
            function open(panelName: string): void;
        }
        
        namespace Message {
            function request(extensionName: string, methodName: string, ...args: any[]): Promise<any>;
        }
    }
}

export {};
