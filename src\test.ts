/**
 * 測試文件 - 用於驗證 UUID 檢查功能
 */

import * as fs from 'fs';
import * as path from 'path';

/**
 * 創建測試用的模擬文件結構
 */
export function createTestFiles(testDir: string) {
    // 確保測試目錄存在
    if (!fs.existsSync(testDir)) {
        fs.mkdirSync(testDir, { recursive: true });
    }

    // 創建測試資源文件
    const testAsset1 = path.join(testDir, 'test-sprite.png');
    const testAsset1Meta = path.join(testDir, 'test-sprite.png.meta');
    
    const testAsset2 = path.join(testDir, 'test-audio.mp3');
    const testAsset2Meta = path.join(testDir, 'test-audio.mp3.meta');
    
    const testScene = path.join(testDir, 'test-scene.scene');
    const testPrefab = path.join(testDir, 'test-prefab.prefab');
    
    // 模擬 UUID
    const uuid1 = '12345678-1234-1234-1234-123456789abc';
    const uuid2 = '*************-4321-4321-cba987654321';
    
    // 創建 meta 文件
    fs.writeFileSync(testAsset1Meta, JSON.stringify({
        uuid: uuid1,
        type: 'sprite',
        importer: 'texture'
    }, null, 2));
    
    fs.writeFileSync(testAsset2Meta, JSON.stringify({
        uuid: uuid2,
        type: 'audio',
        importer: 'audio'
    }, null, 2));
    
    // 創建場景文件（引用第一個資源）
    fs.writeFileSync(testScene, JSON.stringify({
        name: 'TestScene',
        components: [{
            type: 'Sprite',
            spriteFrame: {
                __uuid__: uuid1
            }
        }]
    }, null, 2));
    
    // 創建預製體文件（不引用任何資源）
    fs.writeFileSync(testPrefab, JSON.stringify({
        name: 'TestPrefab',
        components: []
    }, null, 2));
    
    // 創建空的資源文件
    fs.writeFileSync(testAsset1, '');
    fs.writeFileSync(testAsset2, '');
    
    console.log('測試文件已創建:');
    console.log(`- ${testAsset1} (UUID: ${uuid1}) - 被引用`);
    console.log(`- ${testAsset2} (UUID: ${uuid2}) - 孤立資源`);
    console.log(`- ${testScene} - 引用了 ${uuid1}`);
    console.log(`- ${testPrefab} - 沒有引用任何資源`);
}

/**
 * 清理測試文件
 */
export function cleanupTestFiles(testDir: string) {
    if (fs.existsSync(testDir)) {
        fs.rmSync(testDir, { recursive: true, force: true });
        console.log('測試文件已清理');
    }
}

/**
 * 運行測試
 */
export async function runTest() {
    const testDir = path.join(process.cwd(), 'test-assets');
    
    try {
        console.log('=== 開始 UUID 檢查工具測試 ===');
        
        // 創建測試文件
        createTestFiles(testDir);
        
        // 這裡可以調用實際的掃描函數進行測試
        // const results = await scanUuidUsage(testDir);
        
        console.log('\n測試完成！');
        console.log('請在 Cocos Creator 中測試實際功能。');
        
    } catch (error) {
        console.error('測試過程中發生錯誤:', error);
    } finally {
        // 清理測試文件
        cleanupTestFiles(testDir);
    }
}

// 如果直接運行此文件，執行測試
if (require.main === module) {
    runTest();
}
