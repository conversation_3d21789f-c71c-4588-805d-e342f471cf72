/**
 * UUID 檢查工具配置
 */
export interface ScanConfig {
    /** 要掃描的文件擴展名 */
    scanExtensions: string[];
    /** 要排除的目錄 */
    excludeDirectories: string[];
    /** 要排除的文件模式 */
    excludePatterns: string[];
    /** 是否顯示詳細日誌 */
    verbose: boolean;
    /** 最大掃描文件數量 */
    maxFiles: number;
}
/**
 * 默認配置
 */
export declare const defaultConfig: ScanConfig;
/**
 * 獲取配置
 */
export declare function getConfig(): ScanConfig;
/**
 * 檢查文件是否應該被掃描
 */
export declare function shouldScanFile(filePath: string, config: ScanConfig): boolean;
/**
 * 檢查目錄是否應該被掃描
 */
export declare function shouldScanDirectory(dirPath: string, config: ScanConfig): boolean;
