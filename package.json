{"package_version": 2, "version": "1.0.0", "name": "checkUuidUsage", "description": "i18n:checkUuidUsage.description", "main": "./dist/main.js", "devDependencies": {"@types/node": "^16.0.1", "typescript": "^4.3.4"}, "author": "Cocos Creator Developer", "editor": ">=3.6.2", "scripts": {"build": "tsc -b", "watch": "tsc -w"}, "contributions": {"menu": [{"path": "UUID Tools", "label": "Check UUID Usage", "message": "check-uuid-usage"}, {"path": "UUID Tools", "label": "Open Panel", "message": "open-panel"}], "messages": {"check-uuid-usage": {"methods": ["checkUuidUsage"]}, "open-panel": {"methods": ["openPanel"]}, "getUuidUsageData": {"methods": ["getUuidUsageData"]}}, "panels": {"default": {"title": "i18n:checkUuidUsage.panel.title", "type": "dockable", "main": "./dist/panels/default/index.js", "size": {"min-width": 400, "min-height": 300, "width": 800, "height": 600}}}}}