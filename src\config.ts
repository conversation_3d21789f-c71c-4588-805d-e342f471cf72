/**
 * UUID 檢查工具配置
 */

export interface ScanConfig {
    /** 要掃描的文件擴展名 */
    scanExtensions: string[];
    
    /** 要排除的目錄 */
    excludeDirectories: string[];
    
    /** 要排除的文件模式 */
    excludePatterns: string[];
    
    /** 是否顯示詳細日誌 */
    verbose: boolean;
    
    /** 最大掃描文件數量 */
    maxFiles: number;
}

/**
 * 默認配置
 */
export const defaultConfig: ScanConfig = {
    scanExtensions: [
        '.scene',
        '.prefab', 
        '.ts',
        '.js',
        '.json',
        '.meta'
    ],
    
    excludeDirectories: [
        'node_modules',
        '.git',
        'temp',
        'library',
        'local',
        'build'
    ],
    
    excludePatterns: [
        '*.tmp',
        '*.temp',
        '*.bak',
        '*~'
    ],
    
    verbose: false,
    maxFiles: 10000
};

/**
 * 獲取配置
 */
export function getConfig(): ScanConfig {
    // 這裡可以從用戶設置或配置文件中讀取自定義配置
    // 目前返回默認配置
    return { ...defaultConfig };
}

/**
 * 檢查文件是否應該被掃描
 */
export function shouldScanFile(filePath: string, config: ScanConfig): boolean {
    const fileName = filePath.toLowerCase();
    
    // 檢查擴展名
    const hasValidExtension = config.scanExtensions.some(ext => 
        fileName.endsWith(ext.toLowerCase())
    );
    
    if (!hasValidExtension) {
        return false;
    }
    
    // 檢查排除模式
    const isExcluded = config.excludePatterns.some(pattern => {
        const regex = new RegExp(pattern.replace(/\*/g, '.*'));
        return regex.test(fileName);
    });
    
    return !isExcluded;
}

/**
 * 檢查目錄是否應該被掃描
 */
export function shouldScanDirectory(dirPath: string, config: ScanConfig): boolean {
    const dirName = dirPath.toLowerCase();
    
    return !config.excludeDirectories.some(excludeDir => 
        dirName.includes(excludeDir.toLowerCase())
    );
}
