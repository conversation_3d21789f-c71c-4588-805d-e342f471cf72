# checkUuidUsage - Cocos Creator 3.6.2 全局套件

這是一個用於檢查 Cocos Creator 專案中 UUID 使用情況的全局套件。

## 功能特色

- 檢查專案中的 UUID 使用情況
- 分析資源引用關係
- 提供詳細的 UUID 報告

## 安裝與使用

### 1. 安裝依賴

```bash
npm install
```

### 2. 編譯套件

```bash
npm run build
```

### 3. 在 Cocos Creator 中啟用

1. 打開 Cocos Creator 3.6.2
2. 選擇 **擴展 -> 擴展管理器 -> 全局**
3. 找到 `checkUuidUsage` 套件並啟用

### 4. 使用套件

在 Cocos Creator 頂部選單中選擇 **Developer -> Check UUID Usage** 來執行 UUID 檢查。

## 快速開始

### Windows 用戶

```bash
setup.bat
```

### macOS/Linux 用戶

```bash
chmod +x setup.sh
./setup.sh
```

### 手動安裝

```bash
# 安裝依賴
npm install

# 編譯代碼
npm run build
```

## 開發

### 監聽模式

```bash
npm run watch
```

這將啟動 TypeScript 監聽模式，當您修改源代碼時會自動重新編譯。

### 調試

1. 在 Cocos Creator 中打開控制台 (Ctrl+Shift+I)
2. 查看擴展的日誌輸出
3. 使用 `console.log` 進行調試

## 功能詳細說明

### 主要功能

1. **UUID 掃描**: 掃描專案中所有資源的 UUID
2. **引用分析**: 分析每個 UUID 的引用情況
3. **孤立資源檢測**: 找出沒有被引用的資源
4. **視覺化界面**: 提供友好的面板界面

### 支持的文件類型

- `.scene` - 場景文件
- `.prefab` - 預製體文件
- `.ts` / `.js` - 腳本文件
- `.meta` - 資源元數據文件

### 使用場景

- **專案清理**: 找出未使用的資源進行清理
- **依賴分析**: 了解資源之間的依賴關係
- **專案優化**: 減少專案大小，提升性能

## 專案結構

```
checkUuidUsage/
├── @types/                    # TypeScript 類型定義
│   └── packages/
│       └── scene/
│           └── @types/
│               └── cce.d.ts   # Cocos Creator 編輯器類型
├── dist/                      # 編譯後的 JavaScript 代碼
├── i18n/                      # 多語言支持
│   ├── en.js                  # 英文
│   └── zh.js                  # 中文
├── src/                       # TypeScript 源代碼
│   ├── main.ts                # 主入口文件
│   └── panels/
│       └── default/
│           └── index.ts       # 面板代碼
├── package.json               # 套件配置文件
├── tsconfig.json              # TypeScript 配置
├── setup.bat                  # Windows 安裝腳本
├── setup.sh                   # macOS/Linux 安裝腳本
├── .gitignore                 # Git 忽略文件
└── README.md                  # 說明文件
```

## API 參考

### 主要方法

- `checkUuidUsage()`: 在控制台輸出 UUID 檢查結果
- `openPanel()`: 打開 UUID 使用情況面板
- `getUuidUsageData()`: 獲取 UUID 使用情況數據

### 數據結構

```typescript
interface UuidCheckResult {
    uuid: string;           // 資源的 UUID
    assetPath: string;      // 資源路徑
    references: string[];   // 引用該資源的文件列表
    isOrphan: boolean;      // 是否為孤立資源
}
```

## 版本要求

- Cocos Creator >= 3.6.2
- Node.js >= 16.0.1
- TypeScript >= 4.3.4

## 故障排除

### 常見問題

1. **擴展無法加載**
   - 確保已正確編譯 (`npm run build`)
   - 檢查 Cocos Creator 版本是否符合要求

2. **面板無法打開**
   - 確保擴展已啟用
   - 重新啟動 Cocos Creator

3. **掃描結果不準確**
   - 確保專案路徑正確
   - 檢查文件權限

### 開發建議

- 使用 `npm run watch` 進行開發
- 修改代碼後重新啟用擴展
- 查看控制台輸出進行調試
