/**
 * 簡單的空白面板測試
 */

export const template = `
<div class="container">
    <h1>UUID 檢查工具</h1>
    <p>面板已成功載入！</p>
    <ui-button @click="onTestClick">測試按鈕</ui-button>
    <div v-if="message">{{ message }}</div>
</div>
`;

export const style = `
.container {
    padding: 20px;
    text-align: center;
}

.container h1 {
    color: var(--color-default-contrast-emphasis);
    margin-bottom: 20px;
}

.container p {
    color: var(--color-default-contrast-weaken);
    margin-bottom: 20px;
}

.container ui-button {
    margin: 10px;
}
`;

export const $ = {
    container: '.container'
};

export const methods = {
    onTestClick() {
        (this as any).message = '按鈕點擊成功！時間: ' + new Date().toLocaleTimeString();
        console.log('測試按鈕被點擊了');
    }
};

export const data = {
    message: ''
};

export const ready = function() {
    console.log('UUID 檢查面板已準備就緒');
};

export const close = function() {
    console.log('UUID 檢查面板已關閉');
};
