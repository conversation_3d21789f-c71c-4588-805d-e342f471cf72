/**
 * UUID 使用情況檢查面板
 */

interface UuidCheckResult {
    uuid: string;
    assetPath: string;
    references: string[];
    isOrphan: boolean;
}

interface UuidUsageData {
    success: boolean;
    data?: UuidCheckResult[];
    summary?: {
        totalAssets: number;
        orphanAssets: number;
        referencedAssets: number;
    };
    error?: string;
}

export const template = `
<div class="container">
    <div class="header">
        <h2>UUID 使用情況檢查器</h2>
        <ui-button class="scan-btn" @click="onScanClick">掃描專案</ui-button>
    </div>
    
    <div class="summary" v-if="summary">
        <div class="summary-item">
            <span class="label">總資源數:</span>
            <span class="value">{{ summary.totalAssets }}</span>
        </div>
        <div class="summary-item">
            <span class="label">已引用資源:</span>
            <span class="value">{{ summary.referencedAssets }}</span>
        </div>
        <div class="summary-item orphan">
            <span class="label">孤立資源:</span>
            <span class="value">{{ summary.orphanAssets }}</span>
        </div>
    </div>
    
    <div class="content">
        <div v-if="loading" class="loading">
            正在掃描專案...
        </div>
        
        <div v-else-if="error" class="error">
            錯誤: {{ error }}
        </div>
        
        <div v-else-if="results.length > 0" class="results">
            <div class="filter-tabs">
                <ui-button 
                    :class="{ active: currentFilter === 'all' }" 
                    @click="setFilter('all')">
                    全部 ({{ results.length }})
                </ui-button>
                <ui-button 
                    :class="{ active: currentFilter === 'orphan' }" 
                    @click="setFilter('orphan')">
                    孤立資源 ({{ orphanResults.length }})
                </ui-button>
                <ui-button 
                    :class="{ active: currentFilter === 'referenced' }" 
                    @click="setFilter('referenced')">
                    已引用 ({{ referencedResults.length }})
                </ui-button>
            </div>
            
            <div class="result-list">
                <div 
                    v-for="result in filteredResults" 
                    :key="result.uuid" 
                    :class="{ 'result-item': true, 'orphan': result.isOrphan }">
                    <div class="asset-info">
                        <div class="asset-path">{{ result.assetPath }}</div>
                        <div class="uuid">UUID: {{ result.uuid }}</div>
                    </div>
                    <div class="references" v-if="result.references.length > 0">
                        <div class="references-title">引用位置:</div>
                        <div 
                            v-for="ref in result.references" 
                            :key="ref" 
                            class="reference-item">
                            {{ ref }}
                        </div>
                    </div>
                    <div v-else class="no-references">
                        無引用
                    </div>
                </div>
            </div>
        </div>
        
        <div v-else class="empty">
            點擊「掃描專案」開始檢查 UUID 使用情況
        </div>
    </div>
</div>
`;

export const style = `
.container {
    padding: 10px;
    height: 100%;
    display: flex;
    flex-direction: column;
}

.header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid var(--color-normal-border);
}

.header h2 {
    margin: 0;
    color: var(--color-default-contrast-emphasis);
}

.scan-btn {
    min-width: 100px;
}

.summary {
    display: flex;
    gap: 20px;
    margin-bottom: 15px;
    padding: 10px;
    background: var(--color-normal-fill-emphasis);
    border-radius: 4px;
}

.summary-item {
    display: flex;
    flex-direction: column;
    align-items: center;
}

.summary-item .label {
    font-size: 12px;
    color: var(--color-default-contrast-weaken);
    margin-bottom: 4px;
}

.summary-item .value {
    font-size: 18px;
    font-weight: bold;
    color: var(--color-default-contrast-emphasis);
}

.summary-item.orphan .value {
    color: var(--color-warn-contrast-emphasis);
}

.content {
    flex: 1;
    overflow: hidden;
    display: flex;
    flex-direction: column;
}

.loading, .error, .empty {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 200px;
    color: var(--color-default-contrast-weaken);
}

.error {
    color: var(--color-warn-contrast-emphasis);
}

.filter-tabs {
    display: flex;
    gap: 5px;
    margin-bottom: 10px;
}

.filter-tabs ui-button.active {
    background: var(--color-info-fill);
    color: var(--color-info-contrast);
}

.result-list {
    flex: 1;
    overflow-y: auto;
}

.result-item {
    padding: 10px;
    margin-bottom: 8px;
    border: 1px solid var(--color-normal-border);
    border-radius: 4px;
    background: var(--color-normal-fill);
}

.result-item.orphan {
    border-color: var(--color-warn-border);
    background: var(--color-warn-fill-weaken);
}

.asset-info {
    margin-bottom: 8px;
}

.asset-path {
    font-weight: bold;
    color: var(--color-default-contrast-emphasis);
    margin-bottom: 4px;
}

.uuid {
    font-size: 12px;
    color: var(--color-default-contrast-weaken);
    font-family: monospace;
}

.references-title {
    font-size: 12px;
    color: var(--color-default-contrast-weaken);
    margin-bottom: 4px;
}

.reference-item {
    font-size: 12px;
    color: var(--color-info-contrast-emphasis);
    margin-left: 10px;
    margin-bottom: 2px;
}

.no-references {
    font-size: 12px;
    color: var(--color-warn-contrast-emphasis);
    font-style: italic;
}
`;

export const $ = {
    container: '.container'
};

export const methods = {
    async onScanClick() {
        (this as any).loading = true;
        (this as any).error = null;

        try {
            const result: UuidUsageData = await Editor.Message.request('checkUuidUsage', 'getUuidUsageData');

            if (result.success && result.data) {
                (this as any).results = result.data;
                (this as any).summary = result.summary;
            } else {
                (this as any).error = result.error || '掃描失敗';
            }
        } catch (error: any) {
            (this as any).error = `掃描過程中發生錯誤: ${error.message}`;
        } finally {
            (this as any).loading = false;
        }
    },

    setFilter(filter: string) {
        (this as any).currentFilter = filter;
    }
};

export const data = {
    loading: false,
    error: null,
    results: [] as UuidCheckResult[],
    summary: null,
    currentFilter: 'all'
};

export const computed = {
    orphanResults() {
        return (this as any).results.filter((r: UuidCheckResult) => r.isOrphan);
    },

    referencedResults() {
        return (this as any).results.filter((r: UuidCheckResult) => !r.isOrphan);
    },

    filteredResults() {
        switch ((this as any).currentFilter) {
            case 'orphan':
                return (this as any).orphanResults;
            case 'referenced':
                return (this as any).referencedResults;
            default:
                return (this as any).results;
        }
    }
};

export const ready = function() {
    console.log('UUID 使用情況檢查面板已準備就緒');
};

export const close = function() {
    console.log('UUID 使用情況檢查面板已關閉');
};
