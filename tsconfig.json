{"compilerOptions": {"module": "commonjs", "lib": ["es6", "es2017", "es2015", "dom"], "target": "es2015", "declaration": true, "outDir": "./dist/", "strict": false, "esModuleInterop": true, "experimentalDecorators": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "moduleResolution": "node", "downlevelIteration": true, "types": ["node"]}, "include": ["src/**/*", "@types/**/*"], "exclude": ["node_modules", "dist"]}